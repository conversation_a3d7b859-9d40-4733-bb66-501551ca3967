import React, { useState, useEffect, useRef } from "react";
import { Input, Select, Switch, Tooltip, Button } from "antd";
import {
  ChevronLeft,
  GripVertical,
  Search,
  Loader2,
  Plus,
  Trash2,
  Eye,
  FileText,
} from "lucide-react";
import { useDrag, useDrop, DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import useHttp from "../../../hooks/use-http";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import { generateGlobalPreviewHTML } from "../../Components/content";
import TemplateLibrary from "./TemplateLibrary";
import TemplatePreview from "./TemplatePreview";
import TemplateStructure from "./TemplateStructure";

// DND Types
const DND_TYPES = {
  PAGE_ITEM: "PAGE_ITEM",
};

// Device sizes for responsive preview
const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const StructureTab = ({ formData, setFormData }) => {
  const api = useHttp();
  const [pages, setPages] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isPageLibraryOpen, setIsPageLibraryOpen] = useState(true);
  const [isTemplateStructureOpen, setIsTemplateStructureOpen] = useState(true);
  const [selectedPageId, setSelectedPageId] = useState(null);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [scale, setScale] = useState(1);
  const containerRef = useRef(null);

  // Get current device dimensions (default to laptop for template preview)
  const { width: deviceWidth, height: deviceHeight } = DEVICE_SIZES.laptop;

  // Responsive detection
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Fetch pages on component mount
  useEffect(() => {
    setIsLoading(true);
    api.sendRequest(
      CONSTANTS.API.pages.get,
      (res) => {
        console.log("Pages fetched:", res);
        setPages(res);
        setIsLoading(false);
      },
      null,
      null,
      (error) => {
        console.error("Error fetching pages:", error);
        setIsLoading(false);
      }
    );
  }, []);

  // Initialize template pages if not exists
  useEffect(() => {
    if (!formData.pages) {
      setFormData({ ...formData, pages: [] });
    }
  }, [formData, setFormData]);

  // Scale calculation function for preview
  const recalcScale = () => {
    if (!containerRef.current) return;
    const bounds = containerRef.current.getBoundingClientRect();
    const availableWidth = bounds.width - 30;
    const availableHeight = bounds.height - 30;
    const widthScale = availableWidth / deviceWidth;
    const heightScale = availableHeight / deviceHeight;
    setScale(Math.min(widthScale, heightScale, 1));
  };

  useEffect(() => {
    recalcScale();
    const resizeObserver = new ResizeObserver(recalcScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    return () => resizeObserver.disconnect();
  }, [deviceWidth, deviceHeight]);

  // Remove page from template
  const removePageFromTemplate = (pageId) => {
    const updatedPages = formData.pages?.filter((p) => p.id !== pageId) || [];
    setFormData({ ...formData, pages: updatedPages });
    if (selectedPageId === pageId) {
      setSelectedPageId(null);
    }
  };

  // Update page in template
  const updatePageInTemplate = (pageId, updates) => {
    const updatedPages =
      formData.pages?.map((p) =>
        p.id === pageId ? { ...p, ...updates } : p
      ) || [];
    setFormData({ ...formData, pages: updatedPages });
  };

  // Get filtered pages (exclude already added pages)
  const availablePages = pages.filter(
    (page) => !formData.pages?.some((tp) => tp.id === page.id)
  );

  const filteredPages = availablePages.filter((page) =>
    page.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden tw-relative">
        {/* Mobile Backdrop */}
        {isMobile && (isPageLibraryOpen || isTemplateStructureOpen) && (
          <div
            className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-z-40"
            onClick={() => {
              setIsPageLibraryOpen(false);
              setIsTemplateStructureOpen(false);
            }}
          />
        )}

        <TemplateLibrary
          isPageLibraryOpen={isPageLibraryOpen}
          setIsPageLibraryOpen={setIsPageLibraryOpen}
          isMobile={isMobile}
          isTablet={isTablet}
          isLoading={isLoading}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          filteredPages={filteredPages}
        />

        {/* Center Preview Area */}
        <TemplatePreview
          isPageLibraryOpen={isPageLibraryOpen}
          setIsPageLibraryOpen={setIsPageLibraryOpen}
          isTemplateStructureOpen={isTemplateStructureOpen}
          setIsTemplateStructureOpen={setIsTemplateStructureOpen}
          formData={formData}
          setFormData={setFormData}
          pages={pages}
        />

        {/* Right Sidebar - Template Structure */}

        <TemplateStructure
          isTemplateStructureOpen={isTemplateStructureOpen}
          setIsTemplateStructureOpen={setIsTemplateStructureOpen}
          pageData={formData}
          formData={formData}
          setFormData={setFormData}
          removePageFromTemplate={removePageFromTemplate}
        />
        {/* <div
          className={`tw-bg-white tw-border-l tw-border-gray-200 tw-flex tw-flex-col tw-transition-all tw-duration-300 tw-ease-in-out ${
            isTemplateStructureOpen
              ? isMobile
                ? "tw-fixed tw-inset-0 tw-z-50 tw-w-full"
                : isTablet
                ? "tw-w-64"
                : "tw-w-80"
              : "tw-w-0 tw-overflow-hidden"
          } ${isMobile && isTemplateStructureOpen ? "tw-shadow-2xl" : ""}`}
        >
          {isTemplateStructureOpen && (
            <>
              <div className="tw-border-b tw-border-gray-200 tw-flex tw-items-center tw-justify-between">
                <div className="tw-p-3 md:tw-p-4">
                  <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                    Template Structure
                  </h3>
                  <p className="tw-text-sm tw-text-gray-500">
                    {formData.pages?.length || 0} pages
                  </p>
                </div>
                <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
                  <Tooltip title="Hide Template Structure">
                    <button
                      onClick={() => setIsTemplateStructureOpen(false)}
                      className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                    >
                      <ChevronLeft size={30} />
                    </button>
                  </Tooltip>
                </div>
              </div>

              <div className="tw-flex-1 tw-overflow-y-auto tw-p-3 md:tw-p-4">
                {selectedPageId ? (

                  (() => {
                    const selectedPage = formData.pages?.find(
                      (p) => p.id === selectedPageId
                    );
                    if (!selectedPage) return null;

                    return (
                      <div className="tw-space-y-4">
                        <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
                          <h4 className="tw-text-md tw-font-medium tw-text-gray-900">
                            Page Settings
                          </h4>
                          <Button
                            size="small"
                            type="text"
                            icon={<Trash2 className="tw-w-4 tw-h-4" />}
                            onClick={() =>
                              removePageFromTemplate(selectedPageId)
                            }
                            className="tw-text-red-500 hover:tw-text-red-700"
                          />
                        </div>


                        <div>
                          <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-1">
                            Page Title
                          </label>
                          <Input
                            value={selectedPage.name}
                            onChange={(e) =>
                              updatePageInTemplate(selectedPageId, {
                                name: e.target.value,
                              })
                            }
                            placeholder="Enter page title"
                          />
                        </div>


                        <div>
                          <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-1">
                            Version
                          </label>
                          <Select
                            value={selectedPage.version}
                            onChange={(value) =>
                              updatePageInTemplate(selectedPageId, {
                                version: value,
                              })
                            }
                            className="tw-w-full"
                            options={[
                              { label: "v1", value: "v1" },
                              { label: "v2", value: "v2" },
                              { label: "v3", value: "v3" },
                            ]}
                          />
                        </div>


                        <div>
                          <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-1">
                            URL
                          </label>
                          <Input
                            value={selectedPage.url}
                            onChange={(e) =>
                              updatePageInTemplate(selectedPageId, {
                                url: e.target.value,
                              })
                            }
                            placeholder="/page-url"
                            addonBefore="https://example.com"
                          />
                        </div>


                        <div>
                          <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-1">
                            Page Type
                          </label>
                          <Select
                            value={selectedPage.type}
                            onChange={(value) =>
                              updatePageInTemplate(selectedPageId, {
                                type: value,
                              })
                            }
                            className="tw-w-full"
                            options={[
                              { label: "Static Page", value: "static" },
                              { label: "Dynamic Page", value: "dynamic" },
                            ]}
                          />
                        </div>


                        <div>
                          <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
                            <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700">
                              Navigation Bar
                            </label>
                            <Switch
                              checked={selectedPage.showNavbar}
                              onChange={(checked) =>
                                updatePageInTemplate(selectedPageId, {
                                  showNavbar: checked,
                                })
                              }
                            />
                          </div>
                          {selectedPage.showNavbar && (
                            <div>
                              <label className="tw-block tw-text-xs tw-text-gray-500 tw-mb-1">
                                Position of navigation menu
                              </label>
                              <Input
                                type="number"
                                value={selectedPage.navPosition}
                                onChange={(e) =>
                                  updatePageInTemplate(selectedPageId, {
                                    navPosition: parseInt(e.target.value) || 0,
                                  })
                                }
                                placeholder="0"
                                min="0"
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })()
                ) : (
               
                  <div className="tw-space-y-3">
                    {formData.pages?.length > 0 ? (
                      formData.pages.map((templatePage, index) => (
                        <div
                          key={templatePage.id}
                          className="tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200 tw-p-3 tw-cursor-pointer tw-hover:tw-border-gray-300 tw-transition-colors"
                          onClick={() => setSelectedPageId(templatePage.id)}
                        >
                          <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
                            <h5 className="tw-text-sm tw-font-medium tw-text-gray-900">
                              {templatePage.name}
                            </h5>
                            <div className="tw-flex tw-items-center tw-space-x-2">
                              <span className="tw-text-xs tw-text-gray-500">
                                {templatePage.version}
                              </span>
                              <Button
                                size="small"
                                type="text"
                                icon={<Trash2 className="tw-w-3 tw-h-3" />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removePageFromTemplate(templatePage.id);
                                }}
                                className="tw-text-red-500 hover:tw-text-red-700"
                              />
                            </div>
                          </div>
                          <div className="tw-text-xs tw-text-gray-500 tw-space-y-1">
                            <div>URL: {templatePage.url}</div>
                            <div>
                              Type:{" "}
                              {templatePage.type === "static"
                                ? "Static"
                                : "Dynamic"}
                            </div>
                            <div>
                              Navbar: {templatePage.showNavbar ? "Yes" : "No"}
                              {templatePage.showNavbar &&
                                ` (${templatePage.navPosition})`}
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="tw-text-center tw-py-8">
                        <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                        <p className="tw-text-gray-500 tw-mb-2">
                          No pages added
                        </p>
                        <p className="tw-text-sm tw-text-gray-400">
                          Add pages from the library to configure them
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </>
          )}
        </div> */}
      </div>
    </DndProvider>
  );
};

export default StructureTab;
