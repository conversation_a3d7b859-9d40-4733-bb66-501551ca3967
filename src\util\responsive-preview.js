/**
 * Global Responsive Preview Utility
 *
 * This utility provides consistent responsive behavior across all preview components
 * in the application. It handles dynamic height calculation, scaling, and viewport
 * responsiveness to ensure optimal preview display on all screen sizes.
 */

import React from 'react';

// Device sizes for responsive preview (consistent across all components)
export const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

// Global responsive configuration for optimal preview sizing
export const RESPONSIVE_CONFIG = {
  // Minimum container heights to ensure proper preview visibility
  minHeights: {
    mobile: 280,    // Minimum height for mobile preview
    tablet: 320,    // Minimum height for tablet preview  
    laptop: 280,    // Minimum height for laptop preview
  },
  // Maximum container heights to prevent excessive spacing
  maxHeights: {
    mobile: 500,    // Maximum height for mobile preview
    tablet: 600,    // Maximum height for tablet preview
    laptop: 450,    // Maximum height for laptop preview
  },
  // Padding around device frame
  padding: {
    mobile: 20,     // Consistent padding for all devices
    tablet: 20,
    laptop: 20,
  },
  // Scale limits for different screen sizes
  scaleConfig: {
    minScale: 0.15,  // Minimum scale to maintain readability
    maxScale: 1,     // Maximum scale (100%)
    // Responsive scale factors based on viewport
    viewportScaling: {
      small: 0.8,    // < 768px
      medium: 0.9,   // 768px - 1024px  
      large: 1.0,    // > 1024px
    }
  }
};

/**
 * Calculate optimal container height based on available space and device
 * @param {Object} params - Configuration parameters
 * @param {HTMLElement} params.containerRef - Container element reference
 * @param {string} params.previewMode - Current preview mode (mobile/tablet/laptop)
 * @param {number} params.scale - Current scale factor
 * @returns {number} Optimal height in pixels
 */
export const calculateOptimalHeight = ({ containerRef, previewMode, scale }) => {
  if (!containerRef) return RESPONSIVE_CONFIG.minHeights[previewMode];

  const bounds = containerRef.getBoundingClientRect();
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;

  // Determine viewport size category
  const currentViewportSize = viewportWidth < 768 ? 'small' :
    viewportWidth < 1024 ? 'medium' : 'large';

  // Calculate optimal height based on device and viewport
  const config = RESPONSIVE_CONFIG;
  const minHeight = config.minHeights[previewMode];
  const maxHeight = config.maxHeights[previewMode];
  const padding = config.padding[previewMode];
  const deviceHeight = DEVICE_SIZES[previewMode].height;

  // Calculate scaled device height with padding
  const scaledDeviceHeight = (deviceHeight * scale) + (padding * 2);

  // Use scaled device height but constrain within min/max bounds
  let optimalHeight = Math.max(minHeight, Math.min(maxHeight, scaledDeviceHeight));

  // Adjust for smaller viewports to prevent excessive spacing
  if (currentViewportSize === 'small') {
    optimalHeight = Math.min(optimalHeight, viewportHeight * 0.4);
  } else if (currentViewportSize === 'medium') {
    optimalHeight = Math.min(optimalHeight, viewportHeight * 0.5);
  }

  return Math.round(optimalHeight);
};

/**
 * Enhanced scale calculation with responsive considerations
 * @param {Object} params - Configuration parameters
 * @param {HTMLElement} params.containerRef - Container element reference
 * @param {string} params.previewMode - Current preview mode (mobile/tablet/laptop)
 * @returns {number} Optimal scale factor
 */
export const calculateOptimalScale = ({ containerRef, previewMode }) => {
  if (!containerRef) return 1;

  const bounds = containerRef.getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const config = RESPONSIVE_CONFIG;
  const { width: deviceWidth, height: deviceHeight } = DEVICE_SIZES[previewMode];

  // Dynamic padding based on viewport size
  const padding = config.padding[previewMode];
  const availableWidth = bounds.width - (padding * 2);
  const availableHeight = bounds.height - (padding * 2);

  // Calculate base scale
  const widthScale = availableWidth / deviceWidth;
  const heightScale = availableHeight / deviceHeight;
  let optimalScale = Math.min(widthScale, heightScale);

  // Apply viewport-based scaling adjustments
  const viewportScaling = config.scaleConfig.viewportScaling;
  const currentViewportSize = viewportWidth < 768 ? 'small' :
    viewportWidth < 1024 ? 'medium' : 'large';

  optimalScale *= viewportScaling[currentViewportSize];

  // Constrain within scale limits
  optimalScale = Math.max(
    config.scaleConfig.minScale,
    Math.min(config.scaleConfig.maxScale, optimalScale)
  );

  return optimalScale;
};

/**
 * Get viewport size category
 * @returns {string} Viewport size category (small/medium/large)
 */
export const getViewportSize = () => {
  const viewportWidth = window.innerWidth;
  return viewportWidth < 768 ? 'small' :
    viewportWidth < 1024 ? 'medium' : 'large';
};

/**
 * Create responsive preview hook for React components
 * @param {string} previewMode - Current preview mode (mobile/tablet/laptop)
 * @param {React.RefObject} containerRef - Container element reference
 * @returns {Object} Hook state and functions
 */
export const useResponsivePreview = (previewMode, containerRef) => {
  const [scale, setScale] = React.useState(1);
  const [containerHeight, setContainerHeight] = React.useState(0);
  const [viewportSize, setViewportSize] = React.useState('large');

  // Get current device dimensions
  const { width: deviceWidth, height: deviceHeight } = DEVICE_SIZES[previewMode];

  // Enhanced scale calculation with responsive considerations
  const recalcScale = React.useCallback(() => {
    if (!containerRef.current) return;

    const optimalScale = calculateOptimalScale({
      containerRef: containerRef.current,
      previewMode
    });

    const optimalHeight = calculateOptimalHeight({
      containerRef: containerRef.current,
      previewMode,
      scale: optimalScale
    });

    const currentViewportSize = getViewportSize();

    requestAnimationFrame(() => {
      setScale(optimalScale);
      setContainerHeight(optimalHeight);
      setViewportSize(currentViewportSize);
    });
  }, [previewMode, containerRef]);

  // Update scale on mount & when device changes with enhanced responsiveness
  React.useEffect(() => {
    // Initial calculation
    recalcScale();

    // Set up resize observer for container changes
    const resizeObserver = new ResizeObserver(recalcScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    // Set up window resize listener for viewport changes with proper debouncing
    let resizeTimeout;
    const handleWindowResize = () => {
      // Debounce resize events for better performance
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(recalcScale, 150);
    };

    window.addEventListener("resize", handleWindowResize);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener("resize", handleWindowResize);
      clearTimeout(resizeTimeout);
    };
  }, [previewMode, deviceWidth, deviceHeight, recalcScale]);

  return {
    scale,
    containerHeight,
    viewportSize,
    deviceWidth,
    deviceHeight,
    recalcScale,
    config: RESPONSIVE_CONFIG
  };
};

/**
 * Get responsive styles for preview containers
 * @param {Object} params - Style parameters
 * @param {string} params.previewMode - Current preview mode
 * @param {number} params.containerHeight - Container height
 * @returns {Object} Style object for container
 */
export const getResponsiveContainerStyles = ({ previewMode, containerHeight }) => {
  const config = RESPONSIVE_CONFIG;

  return {
    height: `${containerHeight || config.minHeights[previewMode]}px`,
    minHeight: `${config.minHeights[previewMode]}px`,
    maxHeight: `${config.maxHeights[previewMode]}px`,
  };
};

/**
 * Get responsive styles for device frames
 * @param {Object} params - Style parameters
 * @param {string} params.previewMode - Current preview mode
 * @param {number} params.scale - Scale factor
 * @returns {Object} Style object for device frame
 */
export const getResponsiveDeviceStyles = ({ previewMode, scale }) => {
  const { width: deviceWidth, height: deviceHeight } = DEVICE_SIZES[previewMode];

  return {
    width: `${deviceWidth}px`,
    height: `${deviceHeight}px`,
    transform: `scale(${scale})`,
    left: "50%",
    top: "50%",
    marginLeft: `-${deviceWidth / 2}px`,
    marginTop: `-${deviceHeight / 2}px`,
    transition: "all 0.3s ease-in-out",
    transformOrigin: "center center",
  };
};
