/**
 * Global Responsive Preview Utility
 *
 * This utility provides consistent responsive behavior across all preview components
 * in the application. It handles dynamic height calculation, scaling, and viewport
 * responsiveness to ensure optimal preview display on all screen sizes.
 */

import React from 'react';

// Device sizes for responsive preview (consistent across all components)
export const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

// Advanced responsive configuration for optimal preview sizing
export const RESPONSIVE_CONFIG = {
  // Dynamic container heights based on viewport and content
  minHeights: {
    mobile: 320,    // Increased for better mobile visibility
    tablet: 400,    // Better tablet preview size
    laptop: 380,    // Larger laptop preview for better visibility
  },
  // Adaptive maximum heights that scale with viewport
  maxHeights: {
    mobile: 600,    // Allow larger mobile previews
    tablet: 800,    // Increased tablet max height
    laptop: 650,    // Much larger laptop previews for better space utilization
  },
  // Dynamic padding that adapts to screen size and device
  padding: {
    mobile: 15,     // Reduced padding for mobile to maximize space
    tablet: 18,     // Slightly more padding for tablet
    laptop: 25,     // More padding for laptop for better visual separation
  },
  // Advanced scale configuration with viewport-aware limits
  scaleConfig: {
    minScale: 0.2,   // Slightly higher minimum for better readability
    maxScale: 1.2,   // Allow slight upscaling for better space utilization
    // Responsive scale factors optimized for each viewport
    viewportScaling: {
      small: 0.85,   // Better mobile scaling
      medium: 0.95,  // Improved tablet scaling
      large: 1.1,    // Enhanced laptop scaling for better space usage
    },
    // Scale boost factors for different devices
    deviceScaleBoost: {
      mobile: 1.0,   // No boost for mobile (already optimized)
      tablet: 1.05,  // Slight boost for tablet
      laptop: 1.15,  // Significant boost for laptop to utilize space better
    }
  },
  // Advanced gap management between preview items
  gapConfig: {
    // Base gaps between preview items
    baseGaps: {
      mobile: 12,    // Smaller gaps on mobile
      tablet: 16,    // Medium gaps on tablet
      laptop: 20,    // Larger gaps on laptop
    },
    // Viewport-based gap adjustments
    viewportGapMultiplier: {
      small: 0.7,    // Reduce gaps on small screens
      medium: 0.85,  // Slightly reduce gaps on medium screens
      large: 1.0,    // Full gaps on large screens
    }
  }
};

/**
 * Advanced optimal container height calculation with viewport awareness
 * @param {Object} params - Configuration parameters
 * @param {HTMLElement} params.containerRef - Container element reference
 * @param {string} params.previewMode - Current preview mode (mobile/tablet/laptop)
 * @param {number} params.scale - Current scale factor
 * @returns {number} Optimal height in pixels
 */
export const calculateOptimalHeight = ({ containerRef, previewMode, scale }) => {
  if (!containerRef) return RESPONSIVE_CONFIG.minHeights[previewMode];

  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;

  // Determine viewport size category
  const currentViewportSize = viewportWidth < 768 ? 'small' :
    viewportWidth < 1024 ? 'medium' : 'large';

  // Get configuration values
  const config = RESPONSIVE_CONFIG;
  const minHeight = config.minHeights[previewMode];
  const maxHeight = config.maxHeights[previewMode];
  const padding = config.padding[previewMode];
  const deviceHeight = DEVICE_SIZES[previewMode].height;
  const deviceScaleBoost = config.scaleConfig.deviceScaleBoost[previewMode];

  // Apply device-specific scale boost for better space utilization
  const enhancedScale = scale * deviceScaleBoost;

  // Calculate scaled device height with padding
  const scaledDeviceHeight = (deviceHeight * enhancedScale) + (padding * 2);

  // Start with scaled device height
  let optimalHeight = scaledDeviceHeight;

  // Apply viewport-based adjustments for better space utilization
  if (currentViewportSize === 'large') {
    // On large screens, allow larger previews for better visibility
    optimalHeight = Math.min(optimalHeight * 1.1, viewportHeight * 0.6);
  } else if (currentViewportSize === 'medium') {
    // On medium screens, moderate sizing
    optimalHeight = Math.min(optimalHeight, viewportHeight * 0.5);
  } else {
    // On small screens, optimize for space efficiency
    optimalHeight = Math.min(optimalHeight * 0.9, viewportHeight * 0.45);
  }

  // Ensure within min/max bounds
  optimalHeight = Math.max(minHeight, Math.min(maxHeight, optimalHeight));

  return Math.round(optimalHeight);
};

/**
 * Advanced scale calculation with viewport and device awareness
 * @param {Object} params - Configuration parameters
 * @param {HTMLElement} params.containerRef - Container element reference
 * @param {string} params.previewMode - Current preview mode (mobile/tablet/laptop)
 * @returns {number} Optimal scale factor
 */
export const calculateOptimalScale = ({ containerRef, previewMode }) => {
  if (!containerRef) return 1;

  const bounds = containerRef.getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  const config = RESPONSIVE_CONFIG;
  const { width: deviceWidth, height: deviceHeight } = DEVICE_SIZES[previewMode];

  // Dynamic padding based on viewport size
  const padding = config.padding[previewMode];
  const availableWidth = bounds.width - (padding * 2);
  const availableHeight = bounds.height - (padding * 2);

  // Calculate base scale with improved logic
  const widthScale = availableWidth / deviceWidth;
  const heightScale = availableHeight / deviceHeight;
  let optimalScale = Math.min(widthScale, heightScale);

  // Determine viewport size category
  const currentViewportSize = viewportWidth < 768 ? 'small' :
    viewportWidth < 1024 ? 'medium' : 'large';

  // Apply viewport-based scaling adjustments
  const viewportScaling = config.scaleConfig.viewportScaling[currentViewportSize];
  optimalScale *= viewportScaling;

  // Apply device-specific scale boost for better space utilization
  const deviceScaleBoost = config.scaleConfig.deviceScaleBoost[previewMode];
  optimalScale *= deviceScaleBoost;

  // Advanced scaling adjustments based on available space
  if (currentViewportSize === 'large' && previewMode === 'laptop') {
    // On large screens with laptop preview, utilize more space
    const spaceUtilization = Math.min(availableWidth / 800, availableHeight / 600);
    optimalScale = Math.max(optimalScale, spaceUtilization * 0.8);
  }

  // Ensure minimum visibility while maximizing space usage
  if (optimalScale < config.scaleConfig.minScale) {
    optimalScale = config.scaleConfig.minScale;
  } else if (optimalScale > config.scaleConfig.maxScale) {
    optimalScale = config.scaleConfig.maxScale;
  }

  return optimalScale;
};

/**
 * Get viewport size category
 * @returns {string} Viewport size category (small/medium/large)
 */
export const getViewportSize = () => {
  const viewportWidth = window.innerWidth;
  return viewportWidth < 768 ? 'small' :
    viewportWidth < 1024 ? 'medium' : 'large';
};

/**
 * Calculate dynamic gap between preview items
 * @param {string} previewMode - Current preview mode (mobile/tablet/laptop)
 * @returns {number} Gap size in pixels
 */
export const calculateDynamicGap = (previewMode) => {
  const config = RESPONSIVE_CONFIG;
  const viewportSize = getViewportSize();

  const baseGap = config.gapConfig.baseGaps[previewMode];
  const gapMultiplier = config.gapConfig.viewportGapMultiplier[viewportSize];

  return Math.round(baseGap * gapMultiplier);
};

/**
 * Create responsive preview hook for React components
 * @param {string} previewMode - Current preview mode (mobile/tablet/laptop)
 * @param {React.RefObject} containerRef - Container element reference
 * @returns {Object} Hook state and functions
 */
export const useResponsivePreview = (previewMode, containerRef) => {
  const [scale, setScale] = React.useState(1);
  const [containerHeight, setContainerHeight] = React.useState(0);
  const [viewportSize, setViewportSize] = React.useState('large');

  // Get current device dimensions
  const { width: deviceWidth, height: deviceHeight } = DEVICE_SIZES[previewMode];

  // Enhanced scale calculation with responsive considerations
  const recalcScale = React.useCallback(() => {
    if (!containerRef.current) return;

    const optimalScale = calculateOptimalScale({
      containerRef: containerRef.current,
      previewMode
    });

    const optimalHeight = calculateOptimalHeight({
      containerRef: containerRef.current,
      previewMode,
      scale: optimalScale
    });

    const currentViewportSize = getViewportSize();

    requestAnimationFrame(() => {
      setScale(optimalScale);
      setContainerHeight(optimalHeight);
      setViewportSize(currentViewportSize);
    });
  }, [previewMode, containerRef]);

  // Update scale on mount & when device changes with enhanced responsiveness
  React.useEffect(() => {
    // Initial calculation
    recalcScale();

    // Set up resize observer for container changes
    const resizeObserver = new ResizeObserver(recalcScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    // Set up window resize listener for viewport changes with proper debouncing
    let resizeTimeout;
    const handleWindowResize = () => {
      // Debounce resize events for better performance
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(recalcScale, 150);
    };

    window.addEventListener("resize", handleWindowResize);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener("resize", handleWindowResize);
      clearTimeout(resizeTimeout);
    };
  }, [previewMode, deviceWidth, deviceHeight, recalcScale]);

  return {
    scale,
    containerHeight,
    viewportSize,
    deviceWidth,
    deviceHeight,
    recalcScale,
    config: RESPONSIVE_CONFIG
  };
};

/**
 * Get responsive styles for preview containers
 * @param {Object} params - Style parameters
 * @param {string} params.previewMode - Current preview mode
 * @param {number} params.containerHeight - Container height
 * @returns {Object} Style object for container
 */
export const getResponsiveContainerStyles = ({ previewMode, containerHeight }) => {
  const config = RESPONSIVE_CONFIG;

  return {
    height: `${containerHeight || config.minHeights[previewMode]}px`,
    minHeight: `${config.minHeights[previewMode]}px`,
    maxHeight: `${config.maxHeights[previewMode]}px`,
  };
};

/**
 * Get responsive styles for device frames
 * @param {Object} params - Style parameters
 * @param {string} params.previewMode - Current preview mode
 * @param {number} params.scale - Scale factor
 * @returns {Object} Style object for device frame
 */
export const getResponsiveDeviceStyles = ({ previewMode, scale }) => {
  const { width: deviceWidth, height: deviceHeight } = DEVICE_SIZES[previewMode];

  return {
    width: `${deviceWidth}px`,
    height: `${deviceHeight}px`,
    transform: `scale(${scale})`,
    left: "50%",
    top: "50%",
    marginLeft: `-${deviceWidth / 2}px`,
    marginTop: `-${deviceHeight / 2}px`,
    transition: "all 0.3s ease-in-out",
    transformOrigin: "center center",
  };
};
