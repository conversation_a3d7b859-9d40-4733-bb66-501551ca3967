import React, { useRef } from "react";
import { FileText } from "lucide-react";
import { generateGlobalPreviewHTML } from "../../Components/content";
import {
  useResponsivePreview,
  getResponsiveContainerStyles,
  getResponsiveDeviceStyles,
} from "../../../util/responsive-preview";

const SinglePagePreview = ({ templatePage, originalPage, previewMode }) => {
  const containerRef = useRef(null);

  // Use the global responsive preview hook
  const { scale, containerHeight } = useResponsivePreview(
    previewMode,
    containerRef
  );

  // Template Page Preview Component with enhanced responsive design
  const TemplatePagePreview = ({ templatePage, originalPage }) => {
    const generatePagePreviewHTML = () => {
      if (!originalPage) return "";
      return generateGlobalPreviewHTML({
        type: "page",
        data: originalPage.components || [],
        pageData: originalPage,
        customCSS: originalPage.custom_css,
        customJS: originalPage.custom_js,
        title: templatePage.name || originalPage.name,
      });
    };

    // Get responsive styles using utility functions
    const containerStyles = getResponsiveContainerStyles({
      previewMode,
      containerHeight,
    });

    const deviceStyles = getResponsiveDeviceStyles({
      previewMode,
      scale,
    });

    return (
      <div
        className="tw-rounded-xl tw-flex tw-justify-center tw-items-center tw-relative tw-transition-all tw-duration-300"
        style={containerStyles}
      >
        {/* Virtual device frame with consistent positioning */}
        <div
          className="device-frame tw-bg-white tw-rounded-xl tw-border tw-border-gray-200 tw-absolute tw-shadow-lg"
          style={deviceStyles}
        >
          <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-overflow-hidden tw-rounded-xl">
            {originalPage ? (
              <iframe
                srcDoc={generatePagePreviewHTML()}
                className="tw-w-full tw-h-full tw-border-0 tw-rounded-xl"
                title={`${templatePage.name} Preview`}
                style={{
                  pointerEvents: "none",
                  background: "#fff",
                }}
              />
            ) : (
              <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
                <div className="tw-text-center">
                  <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                  <p className="tw-text-gray-500 tw-mb-2">Page not found</p>
                  <p className="tw-text-sm tw-text-gray-400">
                    This page may have been deleted
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      className="tw-w-full tw-relative tw-rounded-lg tw-transition-all tw-duration-300"
      style={getResponsiveContainerStyles({ previewMode, containerHeight })}
    >
      <TemplatePagePreview
        key={templatePage.id}
        templatePage={templatePage}
        originalPage={originalPage}
      />
    </div>
  );
};

export default SinglePagePreview;
